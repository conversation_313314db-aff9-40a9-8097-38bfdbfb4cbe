#include "core/config_manager.h"
#include "utils/file_utils.h"
#include <iostream>

namespace camera_calibration {
namespace core {

ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::loadConfig(const std::string& config_path, const std::string& intrinsics_path) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    main_config_path_ = config_path;
    intrinsics_config_path_ = intrinsics_path;
    
    try {
        // 加载主配置文件
        if (!loadMainConfig(config_path)) {
            std::cerr << "加载主配置文件失败: " << config_path << std::endl;
            return false;
        }
        
        // 加载相机内参配置
        if (!loadIntrinsicsConfig(intrinsics_path)) {
            std::cerr << "加载相机内参配置失败: " << intrinsics_path << std::endl;
            return false;
        }
        
        // 解析配置参数
        if (!parseImageProcessingParams() || !parseBlobDetectorParams() ||
            !parseWorldGridParams() || !parseExtrinsicCalibrationParams()) {
            std::cerr << "解析配置参数失败" << std::endl;
            return false;
        }
        
        config_loaded_ = true;
        return true;
        
    } catch (const YAML::Exception& e) {
        std::cerr << "YAML 解析错误: " << e.what() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cerr << "配置加载错误: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::loadExtrinsicConfig(const std::string& extrinsics_config_path) {
    std::lock_guard<std::mutex> lock(config_mutex_);

    try {
        // 加载外参标定配置文件
        YAML::Node extrinsic_config = YAML::LoadFile(extrinsics_config_path);

        // 将外参配置合并到主配置中
        if (extrinsic_config["extrinsic_calibration"]) {
            main_config_["extrinsic_calibration"] = extrinsic_config["extrinsic_calibration"];
        } else {
            // 如果配置文件根节点就是外参配置
            main_config_["extrinsic_calibration"] = extrinsic_config;
        }

        // 重新解析外参标定参数
        if (!parseExtrinsicCalibrationParams()) {
            std::cerr << "解析外参标定参数失败" << std::endl;
            return false;
        }

        return true;

    } catch (const YAML::Exception& e) {
        std::cerr << "外参配置文件 YAML 解析错误: " << e.what() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cerr << "外参配置文件加载错误: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::reloadConfig() {
    return loadConfig(main_config_path_, intrinsics_config_path_);
}

bool ConfigManager::saveConfig(const std::string& config_path) const {
    // 简单实现，实际项目中可以更完善
    std::cout << "保存配置功能待实现，目标路径: " << config_path << std::endl;
    return true;
}

std::string ConfigManager::getInputImagePath() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    if (!input_image_path_.empty()) {
        return input_image_path_;
    }
    
    try {
        if (main_config_["paths"] && main_config_["paths"]["input_image"]) {
            return main_config_["paths"]["input_image"].as<std::string>();
        }
    } catch (const std::exception& e) {
        std::cerr << "获取输入图像路径失败: " << e.what() << std::endl;
    }
    
    return "";
}

std::string ConfigManager::getSavePath() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    if (!save_path_.empty()) {
        return save_path_;
    }
    
    try {
        if (main_config_["paths"] && main_config_["paths"]["save_path"]) {
            return main_config_["paths"]["save_path"].as<std::string>();
        }
    } catch (const std::exception& e) {
        std::cerr << "获取保存路径失败: " << e.what() << std::endl;
    }
    
    return "./output/";
}


std::string ConfigManager::getDebugPath() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    if (!save_path_.empty()) {
        return save_path_;
    }
    
    try {
        if (main_config_["paths"] && main_config_["paths"]["debug_path"]) {
            return main_config_["paths"]["debug_path"].as<std::string>();
        }
    } catch (const std::exception& e) {
        std::cerr << "获取保存路径失败: " << e.what() << std::endl;
    }
    
    return "./debug/";
}


void ConfigManager::setInputImagePath(const std::string& path) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    input_image_path_ = path;
}

void ConfigManager::setSavePath(const std::string& path) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    save_path_ = path;
}

CameraIntrinsics ConfigManager::getCameraIntrinsics() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (!config_loaded_) {
        return CameraIntrinsics();
    }
    
    try {
        if (intrinsics_config_["camera_parameters"]) {
            auto params = intrinsics_config_["camera_parameters"];
            
            if (params["camera_matrix"]) {
                auto matrix = params["camera_matrix"];
                return CameraIntrinsics(
                    matrix["fx"].as<double>(),
                    matrix["fy"].as<double>(),
                    matrix["cx"].as<double>(),
                    matrix["cy"].as<double>()
                );
            } else if (params["intrinsics_and_distortion"]) {
                auto coeffs = params["intrinsics_and_distortion"].as<std::vector<double>>();
                if (coeffs.size() >= 4) {
                    return CameraIntrinsics(coeffs[0], coeffs[1], coeffs[2], coeffs[3]);
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "获取相机内参失败: " << e.what() << std::endl;
    }
    
    return CameraIntrinsics();
}

DistortionCoefficients ConfigManager::getDistortionCoefficients() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (!config_loaded_) {
        return DistortionCoefficients();
    }
    
    try {
        if (intrinsics_config_["camera_parameters"]) {
            auto params = intrinsics_config_["camera_parameters"];
            
            if (params["intrinsics_and_distortion"]) {
                auto coeffs = params["intrinsics_and_distortion"].as<std::vector<double>>();
                if (coeffs.size() >= 12) {
                    DistortionCoefficients dist;
                    dist.k1 = coeffs[4];
                    dist.k2 = coeffs[5];
                    dist.p1 = coeffs[6];
                    dist.p2 = coeffs[7];
                    dist.k3 = coeffs[8];
                    if (coeffs.size() > 9) dist.k4 = coeffs[9];
                    if (coeffs.size() > 10) dist.k5 = coeffs[10];
                    if (coeffs.size() > 11) dist.k6 = coeffs[11];
                    return dist;
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "获取畸变系数失败: " << e.what() << std::endl;
    }
    
    return DistortionCoefficients();
}

ImageProcessingParams ConfigManager::getImageProcessingParams() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return image_processing_params_;
}

BlobDetectorParams ConfigManager::getBlobDetectorParams() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return blob_detector_params_;
}

// void ConfigManager::printConfig() const {
//     // std::lock_guard<std::mutex> lock(config_mutex_);
    
//     std::cout << "=== 配置信息 ===" << std::endl;
//     std::cout << "=== 配置信息 ===" << std::endl;
//     std::cout << "输入图像: " << getInputImagePath() << std::endl;
//     std::cout << "输出路径: " << getSavePath() << std::endl;
    
//     auto intrinsics = getCameraIntrinsics();
//     std::cout << "相机内参: fx=" << intrinsics.fx << ", fy=" << intrinsics.fy 
//               << ", cx=" << intrinsics.cx << ", cy=" << intrinsics.cy << std::endl;
    
//     std::cout << "图像尺寸: " << image_processing_params_.src_width 
//               << "x" << image_processing_params_.src_height << std::endl;
//     std::cout << "棋盘格: " << image_processing_params_.chess_rows 
//               << "x" << image_processing_params_.chess_cols << std::endl;
// }

// 私有方法实现
bool ConfigManager::loadMainConfig(const std::string& config_path) {
    if (!utils::FileUtils::exists(config_path)) {
        std::cerr << "配置文件不存在: " << config_path << std::endl;
        return false;
    }

    main_config_ = YAML::LoadFile(config_path);
    return true;
}

bool ConfigManager::loadIntrinsicsConfig(const std::string& intrinsics_path) {
    if (!utils::FileUtils::exists(intrinsics_path)) {
        std::cerr << "相机内参文件不存在: " << intrinsics_path << std::endl;
        return false;
    }

    intrinsics_config_ = YAML::LoadFile(intrinsics_path);
    return true;
}

bool ConfigManager::parseImageProcessingParams() {
    try {
        // 解析图像尺寸
        if (main_config_["image_dimensions"]) {
            auto img_dims = main_config_["image_dimensions"];
            image_processing_params_.src_width = img_dims["src_width"].as<int>();
            image_processing_params_.src_height = img_dims["src_height"].as<int>();
            image_processing_params_.output_width = img_dims["output_width"].as<int>();
            image_processing_params_.output_height = img_dims["output_height"].as<int>();
        }

        // 解析棋盘格参数
        if (main_config_["chessboard"]) {
            auto chess = main_config_["chessboard"];
            if (chess["corners"]) {
                image_processing_params_.chess_rows = chess["corners"]["rows"].as<int>();
                image_processing_params_.chess_cols = chess["corners"]["cols"].as<int>();
            }
            if (chess["bounds"]) {
                auto bounds = chess["bounds"];
                image_processing_params_.chess_bounds = cv::Rect(
                    bounds["x_min"].as<int>(),
                    bounds["y_min"].as<int>(),
                    bounds["x_max"].as<int>() - bounds["x_min"].as<int>(),
                    bounds["y_max"].as<int>() - bounds["y_min"].as<int>()
                );
            }
            if (chess["processing"]) {
                auto proc = chess["processing"];
                image_processing_params_.bottom_threshold = proc["bottom_threshold"].as<int>();
                image_processing_params_.chess_y_offset = proc["y_offset"].as<int>();
            }
        }

        // 解析图像处理范围
        if (main_config_["image_processing"]) {
            auto img_proc = main_config_["image_processing"];
            if (img_proc["range"]) {
                auto range = img_proc["range"];
                auto x_range = range["x_range"].as<std::vector<int>>();
                auto y_range = range["y_range"].as<std::vector<int>>();
                image_processing_params_.img_x_range = cv::Range(x_range[0], x_range[1]);
                image_processing_params_.img_y_range = cv::Range(y_range[0], y_range[1]);
            }
            if (img_proc["enhancement"]) {
                image_processing_params_.dark_threshold = img_proc["enhancement"]["dark_threshold"].as<int>();
                image_processing_params_.darken_amount = img_proc["enhancement"]["darken_amount"].as<int>();
                image_processing_params_.msrcr_weights = img_proc["enhancement"]["msrcr"]["weights"].as<std::vector<double>>();
                image_processing_params_.msrcr_sigmas = img_proc["enhancement"]["msrcr"]["sigmas"].as<std::vector<double>>();
                image_processing_params_.msrcr_gain = img_proc["enhancement"]["msrcr"]["gain"].as<double>();
                image_processing_params_.msrcr_offset = img_proc["enhancement"]["msrcr"]["offset"].as<double>();
                image_processing_params_.morphology_kernel_size = img_proc["enhancement"]["morphology"]["kernel_size"].as<std::vector<int>>();
                image_processing_params_.morphology_dilate_iterations = img_proc["enhancement"]["morphology"]["dilate_iterations"].as<int>();
                image_processing_params_.morphology_erode_iterations = img_proc["enhancement"]["morphology"]["erode_iterations"].as<int>();
                image_processing_params_.border_width = img_proc["enhancement"]["border"]["width"].as<int>();
                image_processing_params_.border_bottom_height = img_proc["enhancement"]["border"]["bottom_height"].as<int>();
            }
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "解析图像处理参数失败: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::parseBlobDetectorParams() {
    try {
        if (main_config_["blob_detector"]) {
            auto blob = main_config_["blob_detector"];

            if (blob["color"]) {
                auto color = blob["color"];
                blob_detector_params_.filter_by_color = color["filter_by_color"].as<bool>();
                blob_detector_params_.blob_color = color["blob_color"].as<int>();
            }

            if (blob["area"]) {
                auto area = blob["area"];
                blob_detector_params_.filter_by_area = area["filter_by_area"].as<bool>();
                blob_detector_params_.min_area = area["min_area"].as<float>();
                blob_detector_params_.max_area = area["max_area"].as<float>();
            }

            if (blob["circularity"]) {
                auto circ = blob["circularity"];
                blob_detector_params_.filter_by_circularity = circ["filter_by_circularity"].as<bool>();
                blob_detector_params_.min_circularity = circ["min_circularity"].as<float>();
                blob_detector_params_.max_circularity = circ["max_circularity"].as<float>();
            }

            if (blob["convexity"]) {
                auto conv = blob["convexity"];
                blob_detector_params_.filter_by_convexity = conv["filter_by_convexity"].as<bool>();
                blob_detector_params_.min_convexity = conv["min_convexity"].as<float>();
                blob_detector_params_.max_convexity = conv["max_convexity"].as<float>();
            }

            if (blob["inertia"]) {
                auto inertia = blob["inertia"];
                blob_detector_params_.filter_by_inertia = inertia["filter_by_inertia"].as<bool>();
                blob_detector_params_.min_inertia_ratio = inertia["min_inertia_ratio"].as<float>();
                blob_detector_params_.max_inertia_ratio = inertia["max_inertia_ratio"].as<float>();
            }
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "解析 Blob 检测参数失败: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::parseWorldGridParams() {
    try {
        if (main_config_["world_coordinates"]) {
            auto world = main_config_["world_coordinates"];

            if (world["limits"]) {
                auto limits = world["limits"];
                image_processing_params_.world_x_min = limits["x_min"].as<double>();
                image_processing_params_.world_x_max = limits["x_max"].as<double>();
                image_processing_params_.world_y_min = limits["y_min"].as<double>();
                image_processing_params_.world_y_max = limits["y_max"].as<double>();
            }
            if (world["grid_info"]) {
                auto grid = world["grid_info"];
                world_grid_cols_ = grid["cols"].as<int>();
                world_grid_rows_ = grid["rows"].as<int>();
                distance_to_camera_ = grid["distance_to_camera"].as<double>();
                distance_to_camera_center_ = grid["distance_to_camera_center"].as<double>();

                if (grid["h_axis"]) {
                    world_h_axis_ = grid["h_axis"].as<std::vector<double>>();
                }

                if (grid["w_axis"]) {
                    world_w_axis_ = grid["w_axis"].as<std::vector<double>>();
                }
            }
        }

        if (main_config_["feature_validation"]) {
            auto validation = main_config_["feature_validation"];
            if (validation["column_point_counts"]) {
                auto counts = validation["column_point_counts"];
                if (counts["left"]) {
                    left_column_counts_ = counts["left"].as<std::vector<int>>();
                }
                if (counts["right"]) {
                    right_column_counts_ = counts["right"].as<std::vector<int>>();
                }
            }
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "解析世界网格参数失败: " << e.what() << std::endl;
        return false;
    }
}


// 其他方法的简化实现
void ConfigManager::setCameraIntrinsics(const CameraIntrinsics& intrinsics) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    camera_intrinsics_ = intrinsics;
}

void ConfigManager::setDistortionCoefficients(const DistortionCoefficients& dist_coeffs) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    distortion_coeffs_ = dist_coeffs;
}

void ConfigManager::setImageProcessingParams(const ImageProcessingParams& params) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    image_processing_params_ = params;
}

void ConfigManager::setBlobDetectorParams(const BlobDetectorParams& params) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    blob_detector_params_ = params;
}

cv::Size ConfigManager::getChessboardSize() const {
    return cv::Size(image_processing_params_.chess_cols, image_processing_params_.chess_rows);
}

cv::Rect ConfigManager::getChessboardBounds() const {
    return image_processing_params_.chess_bounds;
}

int ConfigManager::getBottomThreshold() const {
    return image_processing_params_.bottom_threshold;
}

int ConfigManager::getChessYOffset() const {
    return image_processing_params_.chess_y_offset;
}

std::vector<double> ConfigManager::getWorldHAxis() const {
    return world_h_axis_;
}

std::vector<double> ConfigManager::getWorldWAxis() const {
    return world_w_axis_;
}

int ConfigManager::getWorldGridCols() const {
    return world_grid_cols_;
}

int ConfigManager::getWorldGridRows() const {
    return world_grid_rows_;
}

double ConfigManager::getDistanceToCamera() const {
    return distance_to_camera_;
}

double ConfigManager::getDistanceToCameraCenter() const {
    return distance_to_camera_center_;
}

std::vector<int> ConfigManager::getLeftColumnPointCounts() const {
    return left_column_counts_;
}

std::vector<int> ConfigManager::getRightColumnPointCounts() const {
    return right_column_counts_;
}

std::vector<int> ConfigManager::getLeftColumnCounts() const {
    return left_column_counts_;
}

std::vector<int> ConfigManager::getRightColumnCounts() const {
    return right_column_counts_;
}

// 外参标定配置方法实现
ExtrinsicCalibrationParams ConfigManager::getExtrinsicCalibrationParams() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return extrinsic_params_;
}

void ConfigManager::setExtrinsicCalibrationParams(const ExtrinsicCalibrationParams& params) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    extrinsic_params_ = params;
}

CalibrationMode ConfigManager::getCalibrationMode() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return calibration_mode_;
}

void ConfigManager::setCalibrationMode(CalibrationMode mode) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    calibration_mode_ = mode;
}

CalibrationBoard ConfigManager::getCalibrationBoard() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return calibration_board_;
}

void ConfigManager::setCalibrationBoard(const CalibrationBoard& board) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    calibration_board_ = board;
}

bool ConfigManager::parseExtrinsicCalibrationParams() {
    try {
        // 设置默认值
        extrinsic_params_ = ExtrinsicCalibrationParams();
        calibration_mode_ = CalibrationMode::FORWARD_MAPPING;

        // 解析标定模式
        if (main_config_["calibration"]) {
            auto calib_config = main_config_["calibration"];

            if (calib_config["mode"]) {
                std::string mode_str = calib_config["mode"].as<std::string>();
                if (mode_str == "intrinsic_only") {
                    calibration_mode_ = CalibrationMode::INTRINSIC_ONLY;
                } else if (mode_str == "extrinsic_only") {
                    calibration_mode_ = CalibrationMode::EXTRINSIC_ONLY;
                } else if (mode_str == "intrinsic_extrinsic") {
                    calibration_mode_ = CalibrationMode::INTRINSIC_EXTRINSIC;
                } else if (mode_str == "forward_mapping") {
                    calibration_mode_ = CalibrationMode::FORWARD_MAPPING;
                }
            }
        }

        // 解析外参标定参数
        if (main_config_["extrinsic_calibration"]) {
            auto ext_config = main_config_["extrinsic_calibration"];

            // 标定板参数
            if (ext_config["calibration_board"]) {
                auto board_config = ext_config["calibration_board"];

                if (board_config["type"]) {
                    std::string board_type = board_config["type"].as<std::string>();
                    if (board_type == "chessboard") {
                        extrinsic_params_.board_type = CalibrationBoard::CHESSBOARD;
                    } else if (board_type == "circles_grid") {
                        extrinsic_params_.board_type = CalibrationBoard::CIRCLES_GRID;
                    } else if (board_type == "asymmetric_circles_grid") {
                        extrinsic_params_.board_type = CalibrationBoard::ASYMMETRIC_CIRCLES_GRID;
                    }
                }

                if (board_config["board_size"]) {
                    auto size = board_config["board_size"];
                    extrinsic_params_.board_size = cv::Size(size[0].as<int>(), size[1].as<int>());
                }

                if (board_config["square_size"]) {
                    extrinsic_params_.square_size = board_config["square_size"].as<float>();
                }
            }

            // PnP参数
            if (ext_config["pnp_solver"]) {
                auto pnp_config = ext_config["pnp_solver"];

                if (pnp_config["method"]) {
                    std::string method = pnp_config["method"].as<std::string>();
                    if (method == "iterative") {
                        extrinsic_params_.pnp_method = cv::SOLVEPNP_ITERATIVE;
                    } else if (method == "epnp") {
                        extrinsic_params_.pnp_method = cv::SOLVEPNP_EPNP;
                    } else if (method == "p3p") {
                        extrinsic_params_.pnp_method = cv::SOLVEPNP_P3P;
                    }
                }

                if (pnp_config["use_extrinsic_guess"]) {
                    extrinsic_params_.use_extrinsic_guess = pnp_config["use_extrinsic_guess"].as<bool>();
                }

                if (pnp_config["iterations_count"]) {
                    extrinsic_params_.pnp_iterations_count = pnp_config["iterations_count"].as<int>();
                }

                if (pnp_config["reprojection_error"]) {
                    extrinsic_params_.pnp_reprojection_error = pnp_config["reprojection_error"].as<float>();
                }

                if (pnp_config["confidence"]) {
                    extrinsic_params_.pnp_confidence = pnp_config["confidence"].as<double>();
                }
            }

            // Bundle Adjustment参数
            if (ext_config["bundle_adjustment"]) {
                auto ba_config = ext_config["bundle_adjustment"];

                if (ba_config["enable"]) {
                    extrinsic_params_.enable_bundle_adjustment = ba_config["enable"].as<bool>();
                }

                if (ba_config["max_iterations"]) {
                    extrinsic_params_.ba_max_iterations = ba_config["max_iterations"].as<int>();
                }

                if (ba_config["function_tolerance"]) {
                    extrinsic_params_.ba_function_tolerance = ba_config["function_tolerance"].as<double>();
                }

                if (ba_config["parameter_tolerance"]) {
                    extrinsic_params_.ba_parameter_tolerance = ba_config["parameter_tolerance"].as<double>();
                }

                if (ba_config["gradient_tolerance"]) {
                    extrinsic_params_.ba_gradient_tolerance = ba_config["gradient_tolerance"].as<double>();
                }
            }

            // 优化选项
            if (ext_config["optimization"]) {
                auto opt_config = ext_config["optimization"];

                if (opt_config["optimize_intrinsics"]) {
                    extrinsic_params_.optimize_intrinsics = opt_config["optimize_intrinsics"].as<bool>();
                }

                if (opt_config["optimize_distortion"]) {
                    extrinsic_params_.optimize_distortion = opt_config["optimize_distortion"].as<bool>();
                }

                if (opt_config["fix_principal_point"]) {
                    extrinsic_params_.fix_principal_point = opt_config["fix_principal_point"].as<bool>();
                }

                if (opt_config["fix_aspect_ratio"]) {
                    extrinsic_params_.fix_aspect_ratio = opt_config["fix_aspect_ratio"].as<bool>();
                }

                if (opt_config["zero_tangent_distortion"]) {
                    extrinsic_params_.zero_tangent_distortion = opt_config["zero_tangent_distortion"].as<bool>();
                }
            }

            // 质量控制
            if (ext_config["quality_control"]) {
                auto qc_config = ext_config["quality_control"];

                if (qc_config["max_reprojection_error"]) {
                    extrinsic_params_.max_reprojection_error = qc_config["max_reprojection_error"].as<double>();
                }

                if (qc_config["min_required_images"]) {
                    extrinsic_params_.min_required_images = qc_config["min_required_images"].as<int>();
                }

                if (qc_config["min_baseline_ratio"]) {
                    extrinsic_params_.min_baseline_ratio = qc_config["min_baseline_ratio"].as<double>();
                }
            }
        }

        // 初始化标定板
        calibration_board_ = CalibrationBoard(
            extrinsic_params_.board_type,
            extrinsic_params_.board_size,
            extrinsic_params_.square_size
        );

        return true;

    } catch (const std::exception& e) {
        std::cerr << "解析外参标定参数失败: " << e.what() << std::endl;
        return false;
    }
}

} // namespace core
} // namespace camera_calibration
