#include "core/types.h"
#include <opencv2/features2d.hpp>
#include <opencv2/calib3d.hpp>
#include <fstream>
#include <algorithm>
#include <numeric>
#include <cmath>

namespace camera_calibration {
namespace core {

cv::SimpleBlobDetector::Params BlobDetectorParams::toOpenCVParams() const {
    cv::SimpleBlobDetector::Params params;
    
    // 颜色过滤
    params.filterByColor = filter_by_color;
    params.blobColor = blob_color;
    
    // 面积过滤
    params.filterByArea = filter_by_area;
    params.minArea = min_area;
    params.maxArea = max_area;
    
    // 圆度过滤
    params.filterByCircularity = filter_by_circularity;
    params.minCircularity = min_circularity;
    params.maxCircularity = max_circularity;
    
    // 凸性过滤
    params.filterByConvexity = filter_by_convexity;
    params.minConvexity = min_convexity;
    params.maxConvexity = max_convexity;
    
    // 惯性比过滤
    params.filterByInertia = filter_by_inertia;
    params.minInertiaRatio = min_inertia_ratio;
    params.maxInertiaRatio = max_inertia_ratio;
    
    return params;
}

// CameraExtrinsics 方法实现
double CameraExtrinsics::computeReprojectionError(const std::vector<cv::Point3f>& object_points,
                                                  const std::vector<cv::Point2f>& image_points,
                                                  const CameraIntrinsics& intrinsics) const {
    if (object_points.size() != image_points.size() || object_points.empty()) {
        return -1.0;
    }

    // 重投影3D点到2D
    std::vector<cv::Point2f> projected_points;
    cv::Mat camera_matrix = intrinsics.toCameraMatrix();
    cv::Mat dist_coeffs = cv::Mat::zeros(4, 1, CV_64F); // 假设无畸变

    cv::projectPoints(object_points, rotation_vector, translation_vector,
                     camera_matrix, dist_coeffs, projected_points);

    // 计算重投影误差
    double total_error = 0.0;
    for (size_t i = 0; i < image_points.size(); ++i) {
        double dx = image_points[i].x - projected_points[i].x;
        double dy = image_points[i].y - projected_points[i].y;
        total_error += std::sqrt(dx * dx + dy * dy);
    }

    return total_error / image_points.size();
}

// CalibrationBoard 方法实现
void CalibrationBoard::generateObjectPoints() {
    object_points.clear();

    switch (type) {
        case CHESSBOARD:
            for (int i = 0; i < board_size.height; ++i) {
                for (int j = 0; j < board_size.width; ++j) {
                    object_points.push_back(cv::Point3f(j * square_size, i * square_size, 0));
                }
            }
            break;
        case CIRCLES_GRID:
            for (int i = 0; i < board_size.height; ++i) {
                for (int j = 0; j < board_size.width; ++j) {
                    object_points.push_back(cv::Point3f(j * square_size, i * square_size, 0));
                }
            }
            break;
        case ASYMMETRIC_CIRCLES_GRID:
            for (int i = 0; i < board_size.height; ++i) {
                for (int j = 0; j < board_size.width; ++j) {
                    object_points.push_back(cv::Point3f((2 * j + i % 2) * square_size, i * square_size, 0));
                }
            }
            break;
    }
}

bool CalibrationBoard::detectCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners) const {
    corners.clear();

    switch (type) {
        case CHESSBOARD:
            return cv::findChessboardCorners(image, board_size, corners,
                cv::CALIB_CB_ADAPTIVE_THRESH | cv::CALIB_CB_NORMALIZE_IMAGE | cv::CALIB_CB_FAST_CHECK);
        case CIRCLES_GRID:
            return cv::findCirclesGrid(image, board_size, corners, cv::CALIB_CB_SYMMETRIC_GRID);
        case ASYMMETRIC_CIRCLES_GRID:
            return cv::findCirclesGrid(image, board_size, corners, cv::CALIB_CB_ASYMMETRIC_GRID);
        default:
            return false;
    }
}

std::string errorCodeToString(ErrorCode code) {
    switch (code) {
        case ErrorCode::SUCCESS:
            return "成功";
        case ErrorCode::FILE_NOT_FOUND:
            return "文件未找到";
        case ErrorCode::INVALID_CONFIG:
            return "配置无效";
        case ErrorCode::IMAGE_LOAD_FAILED:
            return "图像加载失败";
        case ErrorCode::CALIBRATION_FAILED:
            return "标定失败";
        case ErrorCode::FORWARD_CALIB_FAILED:
            return "前向标定失败";
        case ErrorCode::FEATURE_DETECTION_FAILED:
            return "特征检测失败";
        case ErrorCode::INSUFFICIENT_POINTS:
            return "特征点数量不足";
        case ErrorCode::INVALID_PARAMETERS:
            return "参数无效";
        case ErrorCode::EXTRINSIC_CALIB_FAILED:
            return "外参标定失败";
        case ErrorCode::CORNER_DETECTION_FAILED:
            return "角点检测失败";
        case ErrorCode::PNP_SOLVE_FAILED:
            return "PnP求解失败";
        case ErrorCode::BUNDLE_ADJUSTMENT_FAILED:
            return "束调整优化失败";
        case ErrorCode::INSUFFICIENT_IMAGES:
            return "图像数量不足";
        case ErrorCode::POOR_GEOMETRY:
            return "几何配置不佳";
        case ErrorCode::HIGH_REPROJECTION_ERROR:
            return "重投影误差过高";
        default:
            return "未知错误";
    }
}

// ExtrinsicCalibrationResult 方法实现
void ExtrinsicCalibrationResult::computeStatistics() {
    if (image_data.empty()) {
        return;
    }

    // 计算重投影误差统计
    per_image_errors.clear();
    double total_error = 0.0;
    num_successful_images = 0;

    for (const auto& data : image_data) {
        if (data.corners_found) {
            per_image_errors.push_back(data.reprojection_error);
            total_error += data.reprojection_error;
            std::cout << "重投影误差: " << data.reprojection_error << std::endl;
            num_successful_images++;
            max_reprojection_error = std::max(max_reprojection_error, data.reprojection_error);
        }
    }

    if (num_successful_images > 0) {
        mean_reprojection_error = total_error / num_successful_images;
    }

    total_images = static_cast<int>(image_data.size());

    // 计算几何覆盖度
    if (num_successful_images >= 2) {
        // 计算平移量和旋转角度
        translation_magnitudes.clear();
        rotation_angles.clear();

        for (const auto& data : image_data) {
            if (data.corners_found) {
                // 计算平移量大小
                double tx = data.extrinsics.translation_vector.at<double>(0);
                double ty = data.extrinsics.translation_vector.at<double>(1);
                double tz = data.extrinsics.translation_vector.at<double>(2);
                double translation_magnitude = std::sqrt(tx*tx + ty*ty + tz*tz);
                translation_magnitudes.push_back(translation_magnitude);

                // 计算旋转角度
                double rx = data.extrinsics.rotation_vector.at<double>(0);
                double ry = data.extrinsics.rotation_vector.at<double>(1);
                double rz = data.extrinsics.rotation_vector.at<double>(2);
                double rotation_angle = std::sqrt(rx*rx + ry*ry + rz*rz);
                rotation_angles.push_back(rotation_angle);
            }
        }

        // 计算覆盖度指标
        if (!translation_magnitudes.empty()) {
            auto minmax_trans = std::minmax_element(translation_magnitudes.begin(), translation_magnitudes.end());
            baseline_coverage = *minmax_trans.second - *minmax_trans.first;
        }

        if (!rotation_angles.empty()) {
            auto minmax_rot = std::minmax_element(rotation_angles.begin(), rotation_angles.end());
            rotation_coverage = *minmax_rot.second - *minmax_rot.first;
        }
    }
}

bool ExtrinsicCalibrationResult::saveToFile(const std::string& filename) const {
    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    // 保存基本信息
    file << "# 外参标定结果文件\n";
    file << "success: " << (success ? "true" : "false") << "\n";
    file << "total_images: " << total_images << "\n";
    file << "successful_images: " << num_successful_images << "\n";
    file << "mean_reprojection_error: " << mean_reprojection_error << "\n";
    file << "max_reprojection_error: " << max_reprojection_error << "\n";
    file << "baseline_coverage: " << baseline_coverage << "\n";
    file << "rotation_coverage: " << rotation_coverage << "\n";

    // 保存全局外参
    if (final_extrinsics.has_value()) {
        file << "\n# 最终全局外参\n";
        file << "final_extrinsics:\n";
        const auto& final_ext = final_extrinsics.value();

        file << "  rotation_vector: [";
        for (int j = 0; j < 3; ++j) {
            file << final_ext.rotation_vector.at<double>(j);
            if (j < 2) file << ", ";
        }
        file << "]\n";

        file << "  translation_vector: [";
        for (int j = 0; j < 3; ++j) {
            file << final_ext.translation_vector.at<double>(j);
            if (j < 2) file << ", ";
        }
        file << "]\n";
    }

    if (mean_extrinsics.has_value()) {
        file << "\n# 平均外参\n";
        file << "mean_extrinsics:\n";
        const auto& mean_ext = mean_extrinsics.value();

        file << "  rotation_vector: [";
        for (int j = 0; j < 3; ++j) {
            file << mean_ext.rotation_vector.at<double>(j);
            if (j < 2) file << ", ";
        }
        file << "]\n";

        file << "  translation_vector: [";
        for (int j = 0; j < 3; ++j) {
            file << mean_ext.translation_vector.at<double>(j);
            if (j < 2) file << ", ";
        }
        file << "]\n";
    }

    // 保存每张图像的外参
    file << "\n# 图像外参数据\n";
    for (size_t i = 0; i < image_data.size(); ++i) {
        const auto& data = image_data[i];
        if (data.corners_found) {
            file << "image_" << i << ":\n";
            file << "  path: " << data.image_path << "\n";
            file << "  reprojection_error: " << data.reprojection_error << "\n";

            // 保存旋转向量
            file << "  rotation_vector: [";
            for (int j = 0; j < 3; ++j) {
                file << data.extrinsics.rotation_vector.at<double>(j);
                if (j < 2) file << ", ";
            }
            file << "]\n";

            // 保存平移向量
            file << "  translation_vector: [";
            for (int j = 0; j < 3; ++j) {
                file << data.extrinsics.translation_vector.at<double>(j);
                if (j < 2) file << ", ";
            }
            file << "]\n";
        }
    }

    file.close();
    return true;
}

bool ExtrinsicCalibrationResult::loadFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    // 简单的文件加载实现
    // 在实际应用中，可能需要更复杂的解析逻辑
    std::string line;
    while (std::getline(file, line)) {
        if (line.find("success:") != std::string::npos) {
            success = (line.find("true") != std::string::npos);
        } else if (line.find("mean_reprojection_error:") != std::string::npos) {
            size_t pos = line.find(":") + 1;
            mean_reprojection_error = std::stod(line.substr(pos));
        }
        // 可以继续添加其他字段的解析
    }

    file.close();
    return true;
}

} // namespace core
} // namespace camera_calibration
