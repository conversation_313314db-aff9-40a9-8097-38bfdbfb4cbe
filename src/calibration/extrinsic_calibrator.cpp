#include "calibration/extrinsic_calibrator.h"
#include "calibration/ceres_bundle_adjustment.h"
#include "utils/file_utils.h"
#include "utils/common_utils.h"
#include "core/constants.h"
#include <opencv2/calib3d.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/imgcodecs.hpp>
#include <iostream>
#include <fstream>
#include <algorithm>
#include <filesystem>
#include <chrono>
#include <iomanip>

namespace camera_calibration {
namespace calibration {

ExtrinsicCalibrator::ExtrinsicCalibrator()
    : initialized_(false)
    , debug_mode_(false) {
}

ExtrinsicCalibrator::~ExtrinsicCalibrator() {
}

bool ExtrinsicCalibrator::initialize(const core::CameraIntrinsics& intrinsics,
                                   const core::DistortionCoefficients& dist_coeffs,
                                   const core::ExtrinsicCalibrationParams& params) {
    try {
        // 保存参数
        camera_intrinsics_ = intrinsics;
        distortion_coeffs_ = dist_coeffs;
        calibration_params_ = params;
        
        // 创建 OpenCV 相机矩阵和畸变系数
        camera_matrix_ = intrinsics.toCameraMatrix();
        dist_coeffs_mat_ = dist_coeffs.toDistCoeffs();
      
        // 初始化标定板
        calibration_board_ = core::CalibrationBoard(
            params.board_type,
            params.board_size,
            params.square_size
        );
        
        // 重置统计信息
        last_stats_ = core::CalibrationStats();
        
        initialized_ = true;
        LOG_INFO_FUNC("ExtrinsicCalibrator", "外参标定器初始化成功");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "初始化失败: " + std::string(e.what()));
        return false;
    }
}

core::ErrorCode ExtrinsicCalibrator::calibrateSingleImage(const std::string& image_path,
                                                         core::CameraExtrinsics& extrinsics) {
    if (!initialized_) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "标定器未初始化");
        return core::ErrorCode::INVALID_PARAMETERS;
    }
    
    try {
        // 加载图像
        cv::Mat image;
        if (!loadImage(image_path, image)) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", "图像加载失败: " + image_path);
            return core::ErrorCode::IMAGE_LOAD_FAILED;
        }
        
        // 检测角点
        std::vector<cv::Point2f> corners;
        if (!detectCorners(image, corners)) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", "角点检测失败: " + image_path);
            return core::ErrorCode::CORNER_DETECTION_FAILED;
        }
        
        // 使用PnP求解外参
        cv::Mat rvec, tvec;
        if (!solvePnP(calibration_board_.object_points, corners, rvec, tvec)) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", "PnP求解失败: " + image_path);
            return core::ErrorCode::PNP_SOLVE_FAILED;
        }
        
        // 设置外参
        extrinsics.setFromVectors(rvec, tvec);
        
        // 计算重投影误差
        double reprojection_error = computeReprojectionError(
            calibration_board_.object_points, corners, rvec, tvec);
        
        if (reprojection_error > calibration_params_.max_reprojection_error) {
            LOG_WARN_FUNC("ExtrinsicCalibrator", 
                "重投影误差过高: " + std::to_string(reprojection_error) + " > " + 
                std::to_string(calibration_params_.max_reprojection_error));
            return core::ErrorCode::HIGH_REPROJECTION_ERROR;
        }
        LOG_INFO_FUNC("ExtrinsicCalibrator", 
            "单图像标定成功，重投影误差: " + std::to_string(reprojection_error));
        return core::ErrorCode::SUCCESS;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "单图像标定异常: " + std::string(e.what()));
        return core::ErrorCode::EXTRINSIC_CALIB_FAILED;
    }
}

core::ErrorCode ExtrinsicCalibrator::calibrateMultipleImages(
    const std::vector<std::string>& image_paths,
    core::ExtrinsicCalibrationResult& result) {
    
    if (!initialized_) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "标定器未初始化");
        return core::ErrorCode::INVALID_PARAMETERS;
    }
    
    if (image_paths.size() < static_cast<size_t>(calibration_params_.min_required_images)) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", 
            "图像数量不足: " + std::to_string(image_paths.size()) + " < " + 
            std::to_string(calibration_params_.min_required_images));
        return core::ErrorCode::INSUFFICIENT_IMAGES;
    }
    
    try {
        // 初始化结果
        result = core::ExtrinsicCalibrationResult();
        result.image_data.reserve(image_paths.size());
        result.total_images = static_cast<int>(image_paths.size());
        
        // 处理每张图像
        for (const auto& image_path : image_paths) {
            core::CalibrationImageData image_data;
            image_data.image_path = image_path;
            
            // 加载图像
            if (!loadImage(image_path, image_data.image)) {
                LOG_WARN_FUNC("ExtrinsicCalibrator", "跳过无法加载的图像: " + image_path);
                image_data.corners_found = false;
                result.image_data.push_back(image_data);
                continue;
            }
            
            // 检测角点
            if (!detectCorners(image_data.image, image_data.image_points)) {
                LOG_WARN_FUNC("ExtrinsicCalibrator", "跳过角点检测失败的图像: " + image_path);
                image_data.corners_found = false;
                result.image_data.push_back(image_data);
                continue;
            }
            
            // 设置物理点
            image_data.object_points = calibration_board_.object_points;
            image_data.corners_found = true;
            
            // 使用PnP求解外参
            cv::Mat rvec, tvec;
            if (!solvePnP(image_data.object_points, image_data.image_points, rvec, tvec)) {
                LOG_WARN_FUNC("ExtrinsicCalibrator", "跳过PnP求解失败的图像: " + image_path);
                image_data.corners_found = false;
                result.image_data.push_back(image_data);
                continue;
            }
            
            // 设置外参
            image_data.extrinsics.setFromVectors(rvec, tvec);
            
            // 计算重投影误差
            image_data.reprojection_error = computeReprojectionError(
                image_data.object_points, image_data.image_points, rvec, tvec);
            
            result.image_data.push_back(image_data);
            
            LOG_INFO_FUNC("ExtrinsicCalibrator", 
                "处理图像: " + image_path + ", 重投影误差: " + 
                std::to_string(image_data.reprojection_error));
        }
        
        // 检查成功处理的图像数量
        int successful_images = 0;
        for (const auto& data : result.image_data) {
            if (data.corners_found) {
                successful_images++;
            }
        }
        
        if (successful_images < calibration_params_.min_required_images) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", 
                "成功处理的图像数量不足: " + std::to_string(successful_images) + " < " + 
                std::to_string(calibration_params_.min_required_images));
            result.success = false;
            return core::ErrorCode::INSUFFICIENT_IMAGES;
        }
        
        // 验证几何配置
        if (!validateGeometry(result.image_data)) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", "几何配置验证失败");
            result.success = false;
            return core::ErrorCode::POOR_GEOMETRY;
        }
        
        // Bundle Adjustment优化
        // if (calibration_params_.enable_bundle_adjustment) {
        //     LOG_INFO_FUNC("ExtrinsicCalibrator", "开始Bundle Adjustment优化...");
        //     if (!optimizeExtrinsics(result.image_data, calibration_params_.optimize_intrinsics)) {
        //         LOG_WARN_FUNC("ExtrinsicCalibrator", "Bundle Adjustment优化失败，使用初始结果");
        //     } else {
        //         LOG_INFO_FUNC("ExtrinsicCalibrator", "Bundle Adjustment优化完成");
        //     }
        // }
        
        // 评估标定质量
        evaluateCalibrationQuality(result.image_data, result);
        
        // 更新统计信息
        updateStatistics(result.image_data);

        // 计算全局外参
        computeFinalExtrinsics(result);

        result.success = true;
        LOG_INFO_FUNC("ExtrinsicCalibrator", 
            "多图像外参标定完成，成功处理 " + std::to_string(result.num_successful_images) + 
            " 张图像，平均重投影误差: " + std::to_string(result.mean_reprojection_error));
        
        return core::ErrorCode::SUCCESS;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "多图像标定异常: " + std::string(e.what()));
        result.success = false;
        return core::ErrorCode::EXTRINSIC_CALIB_FAILED;
    }
}

core::ErrorCode ExtrinsicCalibrator::calibrateFromDirectory(
    const std::string& image_directory,
    const std::string& image_pattern,
    core::ExtrinsicCalibrationResult& result) {
    
    // 获取目录中的图像文件
    std::vector<std::string> image_files = getImageFilesFromDirectory(image_directory, image_pattern);
    
    if (image_files.empty()) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", 
            "目录中未找到匹配的图像文件: " + image_directory + "/" + image_pattern);
        return core::ErrorCode::FILE_NOT_FOUND;
    }
    
    LOG_INFO_FUNC("ExtrinsicCalibrator", 
        "从目录加载到 " + std::to_string(image_files.size()) + " 张图像");
    
    // 调用多图像标定
    return calibrateMultipleImages(image_files, result);
}

bool ExtrinsicCalibrator::detectCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners) {
    if (image.empty()) {
        return false;
    }

    // 使用标定板检测角点
    bool found = calibration_board_.detectCorners(image, corners);

    if (found && calibration_params_.enable_subpixel_refinement) {
        // 亚像素精度优化
        cv::Mat gray;
        if (image.channels() == 3) {
            cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
        } else {
            gray = image;
        }

        cv::TermCriteria criteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1);
        cv::cornerSubPix(gray, corners, cv::Size(11, 11), cv::Size(-1, -1), criteria);
    }
    return found;
}

bool ExtrinsicCalibrator::solvePnP(const std::vector<cv::Point3f>& object_points,
                                  const std::vector<cv::Point2f>& image_points,
                                  cv::Mat& rvec, cv::Mat& tvec) {
    if (object_points.size() != image_points.size() || object_points.size() < 4) {
        return false;
    }

    try {
        bool success = cv::solvePnP(
            object_points, image_points,
            camera_matrix_, dist_coeffs_mat_,
            rvec, tvec,
            calibration_params_.use_extrinsic_guess,
            calibration_params_.pnp_method
        );

        return success;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "PnP求解异常: " + std::string(e.what()));
        return false;
    }
}

double ExtrinsicCalibrator::computeReprojectionError(const std::vector<cv::Point3f>& object_points,
                                                    const std::vector<cv::Point2f>& image_points,
                                                    const cv::Mat& rvec, const cv::Mat& tvec) {
    if (object_points.size() != image_points.size() || object_points.empty()) {
        return -1.0;
    }

    // 重投影3D点到2D
    std::vector<cv::Point2f> projected_points;
    cv::projectPoints(object_points, rvec, tvec, camera_matrix_, dist_coeffs_mat_, projected_points);

    // 计算重投影误差
    double total_error = 0.0;
    for (size_t i = 0; i < image_points.size(); ++i) {
        double dx = image_points[i].x - projected_points[i].x;
        double dy = image_points[i].y - projected_points[i].y;
        total_error += std::sqrt(dx * dx + dy * dy);
    }

    return total_error / image_points.size();
}

bool ExtrinsicCalibrator::optimizeExtrinsics(std::vector<core::CalibrationImageData>& image_data) {
    try {
#ifdef USE_CERES_SOLVER
        // 使用Ceres Solver进行Bundle Adjustment优化
        CeresBundleAdjuster bundle_adjuster;

        // 创建内参和畸变系数的副本，以便在优化过程中修改
        core::CameraIntrinsics intrinsics_copy = camera_intrinsics_;
        core::DistortionCoefficients distortion_copy = distortion_coeffs_;

        // 执行Bundle Adjustment优化（仅优化外参）
        bool success = bundle_adjuster.optimize(
            image_data,
            intrinsics_copy,
            distortion_copy,
            calibration_params_,
            false,  // 不优化内参
            false   // 不优化畸变
        );

        if (!success) {
            LOG_WARN_FUNC("ExtrinsicCalibrator", "Ceres Bundle Adjustment优化失败，使用OpenCV fallback");
            // 如果Ceres优化失败，回退到OpenCV实现
            return optimizeExtrinsicsOpenCV(image_data);
        }

        // // 如果优化内参，更新内参
        // if (optimize_intrinsics) {
        //     camera_intrinsics_ = intrinsics_copy;
        //     camera_matrix_ = camera_intrinsics_.toCameraMatrix();
        // }

        // // 如果优化畸变，更新畸变系数
        // if (calibration_params_.optimize_distortion) {
        //     distortion_coeffs_ = distortion_copy;
        //     dist_coeffs_mat_ = distortion_coeffs_.toDistCoeffs();
        // }

        // 重新计算所有图像的重投影误差
        for (auto& data : image_data) {
            if (data.corners_found) {
                data.reprojection_error = computeReprojectionError(
                    data.object_points, data.image_points,
                    data.extrinsics.rotation_vector, data.extrinsics.translation_vector);
            }
        }

        // 获取优化统计信息
        const auto& summary = bundle_adjuster.getSolverSummary();
        LOG_INFO_FUNC("ExtrinsicCalibrator",
            std::string("Ceres Bundle Adjustment优化完成\n") +
            "初始代价: " + std::to_string(summary.initial_cost) + "\n" +
            "最终代价: " + std::to_string(summary.final_cost) + "\n" +
            "迭代次数: " + std::to_string(summary.num_successful_steps) + "\n" +
            "求解时间: " + std::to_string(summary.total_time_in_seconds) + "秒");

        return true;
#else
        // 如果没有Ceres Solver，使用OpenCV fallback实现
        LOG_INFO_FUNC("ExtrinsicCalibrator", "使用OpenCV Bundle Adjustment (Ceres Solver不可用)");
        return optimizeExtrinsicsOpenCV(image_data);
#endif

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "Bundle Adjustment异常: " + std::string(e.what()));
        return false;
    }
}

bool ExtrinsicCalibrator::optimizeExtrinsicsOpenCV(std::vector<core::CalibrationImageData>& image_data) {
    try {
        // 收集所有有效的图像数据
        std::vector<std::vector<cv::Point3f>> object_points_all;
        std::vector<std::vector<cv::Point2f>> image_points_all;

        for (const auto& data : image_data) {
            if (data.corners_found) {
                object_points_all.push_back(data.object_points);
                image_points_all.push_back(data.image_points);
            }
        }

        if (object_points_all.size() < 2) {
            return false;
        }

        // 使用OpenCV的calibrateCamera进行全局优化
        std::vector<cv::Mat> rvecs, tvecs;
        cv::Mat camera_matrix = camera_matrix_.clone();
        cv::Mat dist_coeffs = dist_coeffs_mat_.clone();

        // 固定内参，仅优化外参
        int flags = cv::CALIB_USE_INTRINSIC_GUESS | cv::CALIB_FIX_INTRINSIC;
        if (calibration_params_.fix_principal_point) {
            flags |= cv::CALIB_FIX_PRINCIPAL_POINT;
        }
        if (calibration_params_.fix_aspect_ratio) {
            flags |= cv::CALIB_FIX_ASPECT_RATIO;
        }
        if (calibration_params_.zero_tangent_distortion) {
            flags |= cv::CALIB_ZERO_TANGENT_DIST;
        }

        cv::Size image_size = image_data[0].image.size();
        double rms = cv::calibrateCamera(
            object_points_all, image_points_all, image_size,
            camera_matrix, dist_coeffs, rvecs, tvecs, flags
        );

        // 更新优化后的外参
        size_t valid_idx = 0;
        for (auto& data : image_data) {
            if (data.corners_found && valid_idx < rvecs.size()) {
                data.extrinsics.setFromVectors(rvecs[valid_idx], tvecs[valid_idx]);

                // 重新计算重投影误差
                data.reprojection_error = computeReprojectionError(
                    data.object_points, data.image_points,
                    rvecs[valid_idx], tvecs[valid_idx]);

                valid_idx++;
            }
        }

        // 内参保持不变（仅优化外参）

        LOG_INFO_FUNC("ExtrinsicCalibrator", "OpenCV Bundle Adjustment完成，RMS误差: " + std::to_string(rms));
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "OpenCV Bundle Adjustment异常: " + std::string(e.what()));
        return false;
    }
}

void ExtrinsicCalibrator::evaluateCalibrationQuality(
    const std::vector<core::CalibrationImageData>& image_data,
    core::ExtrinsicCalibrationResult& result) {

    // 计算统计信息
    result.computeStatistics();

    // 计算几何覆盖度
    result.baseline_coverage = computeBaselineCoverage(image_data);
    result.rotation_coverage = computeRotationCoverage(image_data);

    // 计算每张图像的误差
    result.per_image_errors = computePerImageErrors(image_data);
}

// 私有辅助方法实现
bool ExtrinsicCalibrator::loadImage(const std::string& image_path, cv::Mat& image) {
    image = cv::imread(image_path, cv::IMREAD_COLOR);
    return !image.empty();
}

bool ExtrinsicCalibrator::preprocessImage(const cv::Mat& input, cv::Mat& output) {
    if (input.empty()) {
        return false;
    }

    output = input.clone();

    // 可以在这里添加图像预处理步骤
    // 例如：去噪、增强对比度等

    return true;
}

bool ExtrinsicCalibrator::refineCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners) {
    if (image.empty() || corners.empty()) {
        return false;
    }

    cv::Mat gray;
    if (image.channels() == 3) {
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    } else {
        gray = image;
    }

    cv::TermCriteria criteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1);
    cv::cornerSubPix(gray, corners, cv::Size(11, 11), cv::Size(-1, -1), criteria);

    return true;
}

bool ExtrinsicCalibrator::validateGeometry(const std::vector<core::CalibrationImageData>& image_data) {
    // 检查基线覆盖度
    double baseline_coverage = computeBaselineCoverage(image_data);
    if (baseline_coverage < calibration_params_.min_baseline_ratio) {
        LOG_WARN_FUNC("ExtrinsicCalibrator",
            "基线覆盖度不足: " + std::to_string(baseline_coverage) + " < " +
            std::to_string(calibration_params_.min_baseline_ratio));
        return false;
    }

    // 检查旋转覆盖度
    double rotation_coverage = computeRotationCoverage(image_data);
    if (rotation_coverage < 0.1) { // 最小旋转覆盖度阈值
        LOG_WARN_FUNC("ExtrinsicCalibrator",
            "旋转覆盖度不足: " + std::to_string(rotation_coverage));
        return false;
    }

    return true;
}

void ExtrinsicCalibrator::updateStatistics(const std::vector<core::CalibrationImageData>& image_data) {
    last_stats_ = core::CalibrationStats();

    // 计算基本统计信息
    int successful_images = 0;
    double total_error = 0.0;
    double max_error = 0.0;

    for (const auto& data : image_data) {
        if (data.corners_found) {
            successful_images++;
            total_error += data.reprojection_error;
            max_error = std::max(max_error, data.reprojection_error);
        }
    }

    if (successful_images > 0) {
        last_stats_.mean_reprojection_error = total_error / successful_images;
        last_stats_.max_reprojection_error = max_error;
        last_stats_.successful_detections = successful_images;
        last_stats_.processed_images = static_cast<int>(image_data.size());
        last_stats_.translation_range = computeBaselineCoverage(image_data);
        last_stats_.rotation_range = computeRotationCoverage(image_data);
    }
}

double ExtrinsicCalibrator::computeBaselineCoverage(const std::vector<core::CalibrationImageData>& image_data) {
    if (image_data.size() < 2) {
        return 0.0;
    }

    std::vector<double> translation_magnitudes;

    for (const auto& data : image_data) {
        if (data.corners_found) {
            double tx = data.extrinsics.translation_vector.at<double>(0);
            double ty = data.extrinsics.translation_vector.at<double>(1);
            double tz = data.extrinsics.translation_vector.at<double>(2);
            double magnitude = std::sqrt(tx*tx + ty*ty + tz*tz);
            translation_magnitudes.push_back(magnitude);
        }
    }

    if (translation_magnitudes.size() < 2) {
        return 0.0;
    }

    auto minmax = std::minmax_element(translation_magnitudes.begin(), translation_magnitudes.end());
    return *minmax.second - *minmax.first;
}

double ExtrinsicCalibrator::computeRotationCoverage(const std::vector<core::CalibrationImageData>& image_data) {
    if (image_data.size() < 2) {
        return 0.0;
    }

    std::vector<double> rotation_angles;

    for (const auto& data : image_data) {
        if (data.corners_found) {
            double rx = data.extrinsics.rotation_vector.at<double>(0);
            double ry = data.extrinsics.rotation_vector.at<double>(1);
            double rz = data.extrinsics.rotation_vector.at<double>(2);
            double angle = std::sqrt(rx*rx + ry*ry + rz*rz);
            rotation_angles.push_back(angle);
        }
    }

    if (rotation_angles.size() < 2) {
        return 0.0;
    }

    auto minmax = std::minmax_element(rotation_angles.begin(), rotation_angles.end());
    return *minmax.second - *minmax.first;
}

std::vector<double> ExtrinsicCalibrator::computePerImageErrors(const std::vector<core::CalibrationImageData>& image_data) {
    std::vector<double> errors;

    for (const auto& data : image_data) {
        if (data.corners_found) {
            errors.push_back(data.reprojection_error);
        }
    }

    return errors;
}

std::vector<std::string> ExtrinsicCalibrator::getImageFilesFromDirectory(
    const std::string& directory, const std::string& pattern) {

    std::vector<std::string> image_files;

    try {
        // 使用 std::filesystem 遍历目录
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                std::string extension = entry.path().extension().string();

                // 简单的模式匹配
                if (pattern == "*.jpg" || pattern == "*.jpeg") {
                    if (extension == ".jpg" || extension == ".jpeg" || extension == ".JPG" || extension == ".JPEG") {
                        image_files.push_back(entry.path().string());
                    }
                } else if (pattern == "*.png") {
                    if (extension == ".png" || extension == ".PNG") {
                        image_files.push_back(entry.path().string());
                    }
                } else if (pattern == "*.bmp") {
                    if (extension == ".bmp" || extension == ".BMP") {
                        image_files.push_back(entry.path().string());
                    }
                } else if (pattern == "*.*") {
                    if (extension == ".jpg" || extension == ".jpeg" || extension == ".png" ||
                        extension == ".JPG" || extension == ".JPEG" || extension == ".PNG") {
                        image_files.push_back(entry.path().string());
                    }
                }
            }
        }

        // 排序文件名
        std::sort(image_files.begin(), image_files.end());

    } catch (const std::filesystem::filesystem_error& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "目录遍历失败: " + std::string(e.what()));
    }

    return image_files;
}

void ExtrinsicCalibrator::visualizeCalibrationResult(const cv::Mat& image,
                                                    const std::vector<cv::Point2f>& corners,
                                                    const core::CameraExtrinsics& extrinsics,
                                                    cv::Mat& output_image) {
    output_image = image.clone();

    // 绘制检测到的角点
    for (const auto& corner : corners) {
        cv::circle(output_image, corner, 5, cv::Scalar(0, 255, 0), 2);
    }

    // 绘制坐标轴
    drawCoordinateAxes(output_image, extrinsics, 50.0);

    // 绘制重投影点
    std::vector<cv::Point2f> reprojected_points;
    cv::projectPoints(calibration_board_.object_points,
                     extrinsics.rotation_vector, extrinsics.translation_vector,
                     camera_matrix_, dist_coeffs_mat_, reprojected_points);

    drawReprojectedPoints(output_image, corners, reprojected_points);
}

void ExtrinsicCalibrator::drawCoordinateAxes(cv::Mat& image, const core::CameraExtrinsics& extrinsics,
                                           double axis_length) {
    // 定义坐标轴的3D点
    std::vector<cv::Point3f> axis_points = {
        cv::Point3f(0, 0, 0),           // 原点
        cv::Point3f(axis_length, 0, 0), // X轴
        cv::Point3f(0, axis_length, 0), // Y轴
        cv::Point3f(0, 0, -axis_length) // Z轴
    };

    // 投影到图像平面
    std::vector<cv::Point2f> projected_points;
    cv::projectPoints(axis_points, extrinsics.rotation_vector, extrinsics.translation_vector,
                     camera_matrix_, dist_coeffs_mat_, projected_points);

    if (projected_points.size() >= 4) {
        cv::Point2f origin = projected_points[0];
        cv::Point2f x_axis = projected_points[1];
        cv::Point2f y_axis = projected_points[2];
        cv::Point2f z_axis = projected_points[3];

        // 绘制坐标轴
        cv::line(image, origin, x_axis, cv::Scalar(0, 0, 255), 3); // X轴 - 红色
        cv::line(image, origin, y_axis, cv::Scalar(0, 255, 0), 3); // Y轴 - 绿色
        cv::line(image, origin, z_axis, cv::Scalar(255, 0, 0), 3); // Z轴 - 蓝色
    }
}

void ExtrinsicCalibrator::drawReprojectedPoints(cv::Mat& image,
                                              const std::vector<cv::Point2f>& corners,
                                              const std::vector<cv::Point2f>& reprojected_points) {
    if (corners.size() != reprojected_points.size()) {
        return;
    }

    for (size_t i = 0; i < corners.size(); ++i) {
        // 绘制重投影点
        cv::circle(image, reprojected_points[i], 3, cv::Scalar(255, 0, 255), 1);

        // 绘制误差线
        cv::line(image, corners[i], reprojected_points[i], cv::Scalar(0, 255, 255), 1);
    }
}

bool ExtrinsicCalibrator::saveCalibrationResult(const core::ExtrinsicCalibrationResult& result,
                                               const std::string& output_path) {
    try {
        // 创建输出目录
        std::filesystem::create_directories(output_path);

        // 保存外参结果
        std::string extrinsics_file = output_path + "/extrinsics.yaml";
        if (!saveExtrinsicsToFile(result, extrinsics_file)) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", "保存外参文件失败: " + extrinsics_file);
            return false;
        }

        // 保存统计信息
        std::string stats_file = output_path + "/statistics.csv";
        if (!saveStatisticsToFile(result, stats_file)) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", "保存统计文件失败: " + stats_file);
            return false;
        }

        // 保存标定结果文本文件
        std::string result_file = output_path + "/calibration_result.txt";
        if (!result.saveToFile(result_file)) {
            LOG_ERROR_FUNC("ExtrinsicCalibrator", "保存结果文件失败: " + result_file);
            return false;
        }

        // LOG_INFO_FUNC("ExtrinsicCalibrator", "标定结果保存完成: " + output_path);
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "保存标定结果异常: " + std::string(e.what()));
        return false;
    }
}

bool ExtrinsicCalibrator::saveExtrinsicsToFile(const core::ExtrinsicCalibrationResult& result,
                                              const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    // YAML格式保存外参标定结果
    file << "# 外参标定结果文件\n";
    file << "# 生成时间: " << std::chrono::system_clock::to_time_t(std::chrono::system_clock::now()) << "\n\n";

    file << "calibration_info:\n";
    file << "  success: " << (result.success ? "true" : "false") << "\n";
    file << "  total_images: " << result.total_images << "\n";
    file << "  successful_images: " << result.num_successful_images << "\n";
    file << "  mean_reprojection_error: " << std::fixed << std::setprecision(6) << result.mean_reprojection_error << "\n";
    file << "  max_reprojection_error: " << std::fixed << std::setprecision(6) << result.max_reprojection_error << "\n";

    if (result.num_successful_images > 0) {
        file << "  detection_rate: " << std::fixed << std::setprecision(4)
             << (static_cast<double>(result.num_successful_images) / result.total_images * 100.0) << "%\n";
    }

    // 保存全局外参结果
    if (result.final_extrinsics.has_value()) {
        file << "\n# 最终全局外参\n";
        file << "final_extrinsics:\n";
        const auto& final_ext = result.final_extrinsics.value();

        // 旋转向量
        file << "  rotation_vector: [";
        for (int j = 0; j < 3; ++j) {
            if (j > 0) file << ", ";
            file << std::fixed << std::setprecision(8) << final_ext.rotation_vector.at<double>(j);
        }
        file << "]\n";

        // 平移向量
        file << "  translation_vector: [";
        for (int j = 0; j < 3; ++j) {
            if (j > 0) file << ", ";
            file << std::fixed << std::setprecision(8) << final_ext.translation_vector.at<double>(j);
        }
        file << "]\n";

        // 旋转矩阵
        cv::Mat rotation_matrix;
        cv::Rodrigues(final_ext.rotation_vector, rotation_matrix);
        file << "  rotation_matrix:\n";
        for (int row = 0; row < 3; ++row) {
            file << "    - [";
            for (int col = 0; col < 3; ++col) {
                if (col > 0) file << ", ";
                file << std::fixed << std::setprecision(8) << rotation_matrix.at<double>(row, col);
            }
            file << "]\n";
        }
    }

    // 保存平均外参
    if (result.mean_extrinsics.has_value()) {
        file << "\n# 平均外参\n";
        file << "mean_extrinsics:\n";
        const auto& mean_ext = result.mean_extrinsics.value();

        // 旋转向量
        file << "  rotation_vector: [";
        for (int j = 0; j < 3; ++j) {
            if (j > 0) file << ", ";
            file << std::fixed << std::setprecision(8) << mean_ext.rotation_vector.at<double>(j);
        }
        file << "]\n";

        // 平移向量
        file << "  translation_vector: [";
        for (int j = 0; j < 3; ++j) {
            if (j > 0) file << ", ";
            file << std::fixed << std::setprecision(8) << mean_ext.translation_vector.at<double>(j);
        }
        file << "]\n";
    }

    file << "\n# 每张图像的外参信息\n";
    file << "camera_poses:\n";

    for (size_t i = 0; i < result.image_data.size(); ++i) {
        const auto& data = result.image_data[i];
        if (data.corners_found && !data.extrinsics.rotation_vector.empty() && !data.extrinsics.translation_vector.empty()) {
            file << "  - image_id: " << i << "\n";
            file << "    image_path: \"" << data.image_path << "\"\n";
            file << "    reprojection_error: " << std::fixed << std::setprecision(6) << data.reprojection_error << "\n";

            // 旋转向量 (Rodrigues格式)
            file << "    rotation_vector: [";
            for (int j = 0; j < 3; ++j) {
                if (j > 0) file << ", ";
                file << std::fixed << std::setprecision(8) << data.extrinsics.rotation_vector.at<double>(j);
            }
            file << "]\n";

            // 平移向量
            file << "    translation_vector: [";
            for (int j = 0; j < 3; ++j) {
                if (j > 0) file << ", ";
                file << std::fixed << std::setprecision(8) << data.extrinsics.translation_vector.at<double>(j);
            }
            file << "]\n";

            // 旋转矩阵
            cv::Mat rotation_matrix;
            cv::Rodrigues(data.extrinsics.rotation_vector, rotation_matrix);
            file << "    rotation_matrix:\n";
            for (int row = 0; row < 3; ++row) {
                file << "      - [";
                for (int col = 0; col < 3; ++col) {
                    if (col > 0) file << ", ";
                    file << std::fixed << std::setprecision(8) << rotation_matrix.at<double>(row, col);
                }
                file << "]\n";
            }
        }
    }

    file.close();
    LOG_INFO_FUNC("ExtrinsicCalibrator", "外参结果已保存到: " + filename);
    return true;
}

bool ExtrinsicCalibrator::saveStatisticsToFile(const core::ExtrinsicCalibrationResult& result,
                                              const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    // CSV格式的统计信息
    file << "Image,Path,CornersFound,ReprojectionError\n";

    for (size_t i = 0; i < result.image_data.size(); ++i) {
        const auto& data = result.image_data[i];
        file << i << "," << data.image_path << ","
             << (data.corners_found ? "true" : "false") << ","
             << data.reprojection_error << "\n";
    }

    file.close();
    return true;
}

bool ExtrinsicCalibrator::updateParameters(const core::ExtrinsicCalibrationParams& params) {
    try {
        calibration_params_ = params;

        // 重新生成标定板的3D点
        calibration_board_.type = params.board_type;
        calibration_board_.board_size = params.board_size;
        calibration_board_.square_size = params.square_size;
        calibration_board_.generateObjectPoints();

        LOG_INFO_FUNC("ExtrinsicCalibrator", "标定参数已更新");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ExtrinsicCalibrator", "更新标定参数失败: " + std::string(e.what()));
        return false;
    }
}

void ExtrinsicCalibrator::computeFinalExtrinsics(core::ExtrinsicCalibrationResult& result) {
    if (result.image_data.empty()) {
        return;
    }

    // 计算平均外参
    result.mean_extrinsics = computeMeanExtrinsics(result.image_data);

    // 找到最佳外参（重投影误差最小的）
    result.best_extrinsics = findBestExtrinsics(result.image_data);

    // 设置最终外参为最佳外参
    result.final_extrinsics = result.best_extrinsics;

    LOG_INFO_FUNC("ExtrinsicCalibrator", "全局外参计算完成");
}

core::CameraExtrinsics ExtrinsicCalibrator::computeMeanExtrinsics(const std::vector<core::CalibrationImageData>& image_data) {
    core::CameraExtrinsics mean_extrinsics;

    if (image_data.empty()) {
        return mean_extrinsics;
    }

    // 收集所有成功标定的外参
    std::vector<cv::Mat> rotation_vectors;
    std::vector<cv::Mat> translation_vectors;

    for (const auto& data : image_data) {
        if (data.corners_found) {
            rotation_vectors.push_back(data.extrinsics.rotation_vector.clone());
            translation_vectors.push_back(data.extrinsics.translation_vector.clone());
        }
    }

    if (rotation_vectors.empty()) {
        return mean_extrinsics;
    }

    // 计算平均旋转向量
    cv::Mat mean_rotation = cv::Mat::zeros(3, 1, CV_64F);
    for (const auto& rvec : rotation_vectors) {
        mean_rotation += rvec;
    }
    mean_rotation /= static_cast<double>(rotation_vectors.size());

    // 计算平均平移向量
    cv::Mat mean_translation = cv::Mat::zeros(3, 1, CV_64F);
    for (const auto& tvec : translation_vectors) {
        mean_translation += tvec;
    }
    mean_translation /= static_cast<double>(translation_vectors.size());

    mean_extrinsics.rotation_vector = mean_rotation;
    mean_extrinsics.translation_vector = mean_translation;

    return mean_extrinsics;
}

core::CameraExtrinsics ExtrinsicCalibrator::findBestExtrinsics(const std::vector<core::CalibrationImageData>& image_data) {
    core::CameraExtrinsics best_extrinsics;
    double min_error = std::numeric_limits<double>::max();

    for (const auto& data : image_data) {
        if (data.corners_found && data.reprojection_error < min_error) {
            min_error = data.reprojection_error;
            best_extrinsics = data.extrinsics;
        }
    }

    return best_extrinsics;
}

} // namespace calibration
} // namespace camera_calibration
