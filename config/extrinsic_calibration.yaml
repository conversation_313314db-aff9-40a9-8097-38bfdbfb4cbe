# 外参标定配置文件
# 版本: 1.0.0

# 标定模式配置
calibration:
  mode: "extrinsic_only"  # 可选: intrinsic_only, extrinsic_only, intrinsic_extrinsic, forward_mapping

# 外参标定参数
extrinsic_calibration:
  # 标定板配置
  calibration_board:
    type: "chessboard"        # 标定板类型: chessboard, circles_grid, asymmetric_circles_grid
    board_size: [11, 8]       # 标定板尺寸 [宽度, 高度] (内部角点数)
    square_size: 30.0         # 方格大小 (mm)
  
  # PnP求解器配置
  pnp_solver:
    method: "iterative"       # PnP方法: iterative, epnp, p3p, dls, upnp
    use_extrinsic_guess: false # 是否使用外参初值
    iterations_count: 100     # 迭代次数
    reprojection_error: 8.0   # 重投影误差阈值 (像素)
    confidence: 0.99          # 置信度
  
  # Bundle Adjustment配置
  bundle_adjustment:
    enable: true              # 是否启用BA优化
    max_iterations: 100       # 最大迭代次数
    function_tolerance: 1e-6  # 函数收敛阈值
    parameter_tolerance: 1e-8 # 参数收敛阈值
    gradient_tolerance: 1e-10 # 梯度收敛阈值
  
  # 优化选项
  optimization:
    optimize_intrinsics: false      # 是否同时优化内参
    optimize_distortion: false      # 是否同时优化畸变
    fix_principal_point: false      # 是否固定主点
    fix_aspect_ratio: false         # 是否固定纵横比
    zero_tangent_distortion: false  # 是否设置切向畸变为0
  
  # 质量控制
  quality_control:
    max_reprojection_error: 2.0     # 最大允许重投影误差 (像素)
    min_required_images: 3          # 最少需要的图像数量
    min_baseline_ratio: 0.1         # 最小基线比例

# 多图像输入配置
multi_image_input:
  # 图像路径配置
  image_paths:
    # 方式1: 指定图像目录
    input_directory: "../data/extrinsic_calib/"
    image_pattern: "*.jpg"    # 图像文件模式
    
    # 方式2: 指定图像列表文件
    # image_list_file: "../data/extrinsic_calib/image_list.txt"
    
    # 方式3: 直接指定图像路径列表
    # image_files:
    #   - "../data/extrinsic_calib/img_001.jpg"
    #   - "../data/extrinsic_calib/img_002.jpg"
    #   - "../data/extrinsic_calib/img_003.jpg"
  
  # 图像预处理
  preprocessing:
    resize_images: false      # 是否调整图像大小
    target_size: [1600, 1200]  # 目标尺寸
    undistort_images: true    # 是否去畸变
    enhance_contrast: false   # 是否增强对比度

# 角点检测配置
corner_detection:
  # 棋盘格角点检测参数
  chessboard:
    adaptive_thresh: true     # 自适应阈值
    normalize_image: true     # 图像归一化
    fast_check: true          # 快速检查
    
  # 亚像素精度优化
  subpixel_refinement:
    enable: true              # 是否启用亚像素优化
    window_size: [11, 11]     # 搜索窗口大小
    zero_zone: [-1, -1]       # 死区大小 (-1表示无死区)
    criteria:
      type: "eps_count"       # 终止条件类型
      max_iterations: 30      # 最大迭代次数
      epsilon: 0.01            # 精度阈值

# 结果输出配置
output:
  # 保存路径
  save_path: "./output/extrinsic_calibration/"
  
  # 保存选项
  save_options:
    save_extrinsics: true           # 保存外参结果
    save_reprojection_images: true # 保存重投影可视化图像
    save_statistics: true          # 保存统计信息
    save_camera_poses: true        # 保存相机姿态
    save_calibration_report: true  # 保存标定报告
  
  # 文件格式
  file_formats:
    extrinsics_format: "yaml"       # 外参保存格式: yaml, xml, json
    statistics_format: "csv"        # 统计信息格式: csv, txt
    images_format: "png"            # 图像保存格式: png, jpg, bmp
  
  # 可视化选项
  visualization:
    draw_axes: true                 # 绘制坐标轴
    axes_length: 50.0               # 坐标轴长度 (mm)
    draw_reprojected_points: true   # 绘制重投影点
    point_color: [0, 255, 0]        # 点的颜色 (BGR)
    line_thickness: 2               # 线条粗细

# 验证配置
validation:
  # 交叉验证
  cross_validation:
    enable: false             # 是否启用交叉验证
    k_folds: 5               # K折交叉验证
    
  # 几何一致性检查
  geometry_check:
    enable: true              # 是否启用几何检查
    max_translation_error: 10.0  # 最大平移误差 (mm)
    max_rotation_error: 5.0      # 最大旋转误差 (度)
    
  # 重投影误差分析
  reprojection_analysis:
    enable: true              # 是否启用重投影分析
    error_threshold: 1.0      # 误差阈值 (像素)
    outlier_ratio: 0.1        # 异常值比例阈值

# 调试配置
debug:
  enable_debug_output: true   # 启用调试输出
  save_intermediate_results: true  # 保存中间结果
  verbose_logging: true       # 详细日志
  debug_image_scale: 0.5      # 调试图像缩放比例
