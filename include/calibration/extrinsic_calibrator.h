#ifndef CAMERA_CALIBRATION_EXTRINSIC_CALIBRATOR_H
#define CAMERA_CALIBRATION_EXTRINSIC_CALIBRATOR_H

#include "core/types.h"
#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>

namespace camera_calibration {
namespace calibration {

/**
 * @brief 外参标定器类
 * 
 * 负责执行相机外参标定，包括：
 * - 多图像角点检测
 * - PnP姿态估计
 * - Bundle Adjustment优化
 * - 标定质量评估
 */
class ExtrinsicCalibrator {
public:
    /**
     * @brief 构造函数
     */
    ExtrinsicCalibrator();
    
    /**
     * @brief 析构函数
     */
    ~ExtrinsicCalibrator();
    
    /**
     * @brief 初始化外参标定器
     * @param intrinsics 相机内参
     * @param dist_coeffs 畸变系数
     * @param params 外参标定参数
     * @return 初始化是否成功
     */
    bool initialize(const core::CameraIntrinsics& intrinsics,
                   const core::DistortionCoefficients& dist_coeffs,
                   const core::ExtrinsicCalibrationParams& params);
    
    /**
     * @brief 执行单张图像的外参标定
     * @param image_path 图像路径
     * @param extrinsics 输出的外参
     * @return 标定结果错误码
     */
    core::ErrorCode calibrateSingleImage(const std::string& image_path,
                                        core::CameraExtrinsics& extrinsics);
    
    /**
     * @brief 执行多张图像的外参标定
     * @param image_paths 图像路径列表
     * @param result 标定结果
     * @return 标定结果错误码
     */
    core::ErrorCode calibrateMultipleImages(const std::vector<std::string>& image_paths,
                                           core::ExtrinsicCalibrationResult& result);
    
    /**
     * @brief 从目录加载图像进行标定
     * @param image_directory 图像目录
     * @param image_pattern 图像文件模式 (如 "*.jpg")
     * @param result 标定结果
     * @return 标定结果错误码
     */
    core::ErrorCode calibrateFromDirectory(const std::string& image_directory,
                                          const std::string& image_pattern,
                                          core::ExtrinsicCalibrationResult& result);
    
    /**
     * @brief 优化外参 (Bundle Adjustment)
     * @param image_data 图像数据列表
     * @return 优化是否成功
     */
    bool optimizeExtrinsics(std::vector<core::CalibrationImageData>& image_data);
    
    /**
     * @brief 评估标定质量
     * @param image_data 图像数据列表
     * @param result 标定结果
     */
    void evaluateCalibrationQuality(const std::vector<core::CalibrationImageData>& image_data,
                                   core::ExtrinsicCalibrationResult& result);
    
    /**
     * @brief 检测图像中的角点
     * @param image 输入图像
     * @param corners 输出角点
     * @return 检测是否成功
     */
    bool detectCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners);
    
    /**
     * @brief 使用PnP求解相机姿态
     * @param object_points 3D物理点
     * @param image_points 2D图像点
     * @param rvec 输出旋转向量
     * @param tvec 输出平移向量
     * @return 求解是否成功
     */
    bool solvePnP(const std::vector<cv::Point3f>& object_points,
                  const std::vector<cv::Point2f>& image_points,
                  cv::Mat& rvec, cv::Mat& tvec);
    
    /**
     * @brief 计算重投影误差
     * @param object_points 3D物理点
     * @param image_points 2D图像点
     * @param rvec 旋转向量
     * @param tvec 平移向量
     * @return 重投影误差
     */
    double computeReprojectionError(const std::vector<cv::Point3f>& object_points,
                                   const std::vector<cv::Point2f>& image_points,
                                   const cv::Mat& rvec, const cv::Mat& tvec);
    
    /**
     * @brief 可视化标定结果
     * @param image 输入图像
     * @param corners 检测到的角点
     * @param extrinsics 外参
     * @param output_image 输出可视化图像
     */
    void visualizeCalibrationResult(const cv::Mat& image,
                                   const std::vector<cv::Point2f>& corners,
                                   const core::CameraExtrinsics& extrinsics,
                                   cv::Mat& output_image);
    
    /**
     * @brief 保存标定结果
     * @param result 标定结果
     * @param output_path 输出路径
     * @return 保存是否成功
     */
    bool saveCalibrationResult(const core::ExtrinsicCalibrationResult& result,
                              const std::string& output_path);
    
    /**
     * @brief 设置调试模式
     * @param enable 是否启用调试模式
     */
    void setDebugMode(bool enable) { debug_mode_ = enable; }

    /**
     * @brief 获取调试模式状态
     */
    bool isDebugMode() const { return debug_mode_; }

    /**
     * @brief 更新标定参数
     * @param params 新的标定参数
     * @return 更新是否成功
     */
    bool updateParameters(const core::ExtrinsicCalibrationParams& params);
    
    /**
     * @brief 获取最后的标定统计信息
     */
    core::CalibrationStats getLastCalibrationStats() const { return last_stats_; }

private:
    // 初始化状态
    bool initialized_;
    bool debug_mode_;
    
    // 相机参数
    core::CameraIntrinsics camera_intrinsics_;
    core::DistortionCoefficients distortion_coeffs_;
    core::ExtrinsicCalibrationParams calibration_params_;
    
    // 标定板
    core::CalibrationBoard calibration_board_;
    
    // OpenCV 相机矩阵和畸变系数
    cv::Mat camera_matrix_;
    cv::Mat dist_coeffs_mat_;
    
    // 统计信息
    core::CalibrationStats last_stats_;
    
    // 私有辅助方法
    bool loadImage(const std::string& image_path, cv::Mat& image);
    bool preprocessImage(const cv::Mat& input, cv::Mat& output);
    bool refineCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners);
    bool validateGeometry(const std::vector<core::CalibrationImageData>& image_data);
    void updateStatistics(const std::vector<core::CalibrationImageData>& image_data);
    
    // OpenCV fallback Bundle Adjustment实现
    bool optimizeExtrinsicsOpenCV(std::vector<core::CalibrationImageData>& image_data);

    // 全局外参计算
    void computeFinalExtrinsics(core::ExtrinsicCalibrationResult& result);
    core::CameraExtrinsics computeMeanExtrinsics(const std::vector<core::CalibrationImageData>& image_data);
    core::CameraExtrinsics findBestExtrinsics(const std::vector<core::CalibrationImageData>& image_data);
    
    // 质量评估相关
    double computeBaselineCoverage(const std::vector<core::CalibrationImageData>& image_data);
    double computeRotationCoverage(const std::vector<core::CalibrationImageData>& image_data);
    std::vector<double> computePerImageErrors(const std::vector<core::CalibrationImageData>& image_data);
    
    // 可视化相关
    void drawCoordinateAxes(cv::Mat& image, const core::CameraExtrinsics& extrinsics, 
                           double axis_length = 50.0);
    void drawReprojectedPoints(cv::Mat& image, const std::vector<cv::Point2f>& corners,
                              const std::vector<cv::Point2f>& reprojected_points);
    
    // 文件I/O相关
    std::vector<std::string> getImageFilesFromDirectory(const std::string& directory,
                                                       const std::string& pattern);
    bool saveExtrinsicsToFile(const core::ExtrinsicCalibrationResult& result,
                             const std::string& filename);
    bool saveStatisticsToFile(const core::ExtrinsicCalibrationResult& result,
                             const std::string& filename);
};

} // namespace calibration
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_EXTRINSIC_CALIBRATOR_H
