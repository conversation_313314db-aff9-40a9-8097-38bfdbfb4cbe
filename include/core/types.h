#ifndef CAMERA_CALIBRATION_CORE_TYPES_H
#define CAMERA_CALIBRATION_CORE_TYPES_H

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>
#include <optional>

namespace camera_calibration {
namespace core {

/**
 * @brief 2D 点信息结构
 */
struct Point2D {
    double x;
    double y;
    
    Point2D() : x(0.0), y(0.0) {}
    Point2D(double x_val, double y_val) : x(x_val), y(y_val) {}
    
    // 转换为 OpenCV Point2f
    cv::Point2f toCvPoint() const {
        return cv::Point2f(static_cast<float>(x), static_cast<float>(y));
    }
    
    // 从 OpenCV Point2f 创建
    static Point2D fromCvPoint(const cv::Point2f& pt) {
        return Point2D(pt.x, pt.y);
    }
};

/**
 * @brief 3D 点信息结构
 */
struct Point3D {
    double x;
    double y;
    double z;
    
    Point3D() : x(0.0), y(0.0), z(0.0) {}
    Point3D(double x_val, double y_val, double z_val) : x(x_val), y(y_val), z(z_val) {}
    
    // 转换为 OpenCV Point3f
    cv::Point3f toCvPoint() const {
        return cv::Point3f(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z));
    }
    
    // 从 OpenCV Point3f 创建
    static Point3D fromCvPoint(const cv::Point3f& pt) {
        return Point3D(pt.x, pt.y, pt.z);
    }
};

/**
 * @brief 相机内参结构
 */
struct CameraIntrinsics {
    double fx;          // x 方向焦距
    double fy;          // y 方向焦距
    double cx;          // x 方向主点
    double cy;          // y 方向主点
    
    CameraIntrinsics() : fx(0.0), fy(0.0), cx(0.0), cy(0.0) {}
    CameraIntrinsics(double fx_val, double fy_val, double cx_val, double cy_val)
        : fx(fx_val), fy(fy_val), cx(cx_val), cy(cy_val) {}
    
    // 转换为 OpenCV 相机矩阵
    cv::Mat toCameraMatrix() const {
        cv::Mat camera_matrix = cv::Mat::eye(3, 3, CV_64F);
        camera_matrix.at<double>(0, 0) = fx;
        camera_matrix.at<double>(1, 1) = fy;
        camera_matrix.at<double>(0, 2) = cx;
        camera_matrix.at<double>(1, 2) = cy;
        return camera_matrix;
    }
};

/**
 * @brief 畸变系数结构
 */
struct DistortionCoefficients {
    double k1, k2, k3;     // 径向畸变系数
    double p1, p2;         // 切向畸变系数
    double k4, k5, k6;     // 高阶径向畸变系数（可选）
    
    DistortionCoefficients() : k1(0.0), k2(0.0), k3(0.0), p1(0.0), p2(0.0), k4(0.0), k5(0.0), k6(0.0) {}
    
    // 转换为 OpenCV 畸变系数向量
    cv::Mat toDistCoeffs() const {
        cv::Mat dist_coeffs = cv::Mat::zeros(8, 1, CV_64F);
        dist_coeffs.at<double>(0) = k1;
        dist_coeffs.at<double>(1) = k2;
        dist_coeffs.at<double>(2) = p1;
        dist_coeffs.at<double>(3) = p2;
        dist_coeffs.at<double>(4) = k3;
        dist_coeffs.at<double>(5) = k4;
        dist_coeffs.at<double>(6) = k5;
        dist_coeffs.at<double>(7) = k6;
        return dist_coeffs;
    }
    
    // 从数组创建
    static DistortionCoefficients fromArray(const double* coeffs, size_t size) {
        DistortionCoefficients dist;
        if (size >= 1) dist.k1 = coeffs[0];
        if (size >= 2) dist.k2 = coeffs[1];
        if (size >= 3) dist.p1 = coeffs[2];
        if (size >= 4) dist.p2 = coeffs[3];
        if (size >= 5) dist.k3 = coeffs[4];
        if (size >= 6) dist.k4 = coeffs[5];
        if (size >= 7) dist.k5 = coeffs[6];
        if (size >= 8) dist.k6 = coeffs[7];
        return dist;
    }
};

/**
 * @brief 标定结果结构
 */
struct CalibrationResult {
    Point3D world_position;    // 世界坐标位置
    Point2D pixel_position;    // 像素坐标位置
    Point2D left_top;          // 左上角点
    Point2D right_top;         // 右上角点
    Point2D right_bottom;      // 右下角点
    Point2D left_bottom;       // 左下角点
    double confidence;         // 置信度
    
    CalibrationResult() : confidence(0.0) {}
};

/**
 * @brief 特征点信息结构
 */
struct FeaturePoint {
    Point2D pixel_coord;       // 像素坐标
    cv::Vec2d polar_coord;     // 极坐标 (距离, 角度)
    cv::Point label;           // 标签 (列, 行)
    double area;               // 面积
    double circularity;        // 圆度

    FeaturePoint() : area(0.0), circularity(0.0) {}
};

/**
 * @brief 相机外参结构
 */
struct CameraExtrinsics {
    cv::Mat rotation_matrix;    // 3x3 旋转矩阵
    cv::Mat translation_vector; // 3x1 平移向量
    cv::Mat rotation_vector;    // 3x1 旋转向量 (Rodrigues表示)

    CameraExtrinsics() {
        rotation_matrix = cv::Mat::eye(3, 3, CV_64F);
        translation_vector = cv::Mat::zeros(3, 1, CV_64F);
        rotation_vector = cv::Mat::zeros(3, 1, CV_64F);
    }

    CameraExtrinsics(const cv::Mat& rvec, const cv::Mat& tvec) {
        setFromVectors(rvec, tvec);
    }

    // 从旋转向量和平移向量设置外参
    void setFromVectors(const cv::Mat& rvec, const cv::Mat& tvec) {
        rvec.copyTo(rotation_vector);
        tvec.copyTo(translation_vector);
        cv::Rodrigues(rvec, rotation_matrix);
    }

    // 从旋转矩阵和平移向量设置外参
    void setFromMatrix(const cv::Mat& rmat, const cv::Mat& tvec) {
        rmat.copyTo(rotation_matrix);
        tvec.copyTo(translation_vector);
        cv::Rodrigues(rmat, rotation_vector);
    }

    // 获取4x4变换矩阵
    cv::Mat getTransformMatrix() const {
        cv::Mat transform = cv::Mat::eye(4, 4, CV_64F);
        rotation_matrix.copyTo(transform(cv::Rect(0, 0, 3, 3)));
        translation_vector.copyTo(transform(cv::Rect(3, 0, 1, 3)));
        return transform;
    }

    // 计算重投影误差
    double computeReprojectionError(const std::vector<cv::Point3f>& object_points,
                                   const std::vector<cv::Point2f>& image_points,
                                   const CameraIntrinsics& intrinsics) const;
};

/**
 * @brief 标定板信息结构
 */
struct CalibrationBoard {
    enum BoardType {
        CHESSBOARD,     // 棋盘格
        CIRCLES_GRID,   // 圆形网格
        ASYMMETRIC_CIRCLES_GRID  // 非对称圆形网格
    };

    BoardType type;
    cv::Size board_size;        // 板子尺寸 (内部角点数)
    float square_size;          // 方格大小 (mm)
    std::vector<cv::Point3f> object_points;  // 3D物理坐标点

    CalibrationBoard() : type(CHESSBOARD), square_size(25.0f) {}

    CalibrationBoard(BoardType board_type, const cv::Size& size, float sq_size)
        : type(board_type), board_size(size), square_size(sq_size) {
        generateObjectPoints();
    }

    // 生成3D物理坐标点
    void generateObjectPoints();

    // 检测角点
    bool detectCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners) const;
};

/**
 * @brief 单张图像的标定数据
 */
struct CalibrationImageData {
    std::string image_path;                    // 图像路径
    cv::Mat image;                            // 图像数据
    std::vector<cv::Point2f> image_points;    // 检测到的2D角点
    std::vector<cv::Point3f> object_points;   // 对应的3D物理点
    bool corners_found;                       // 是否成功检测到角点
    double reprojection_error;               // 重投影误差
    CameraExtrinsics extrinsics;             // 该图像对应的外参

    CalibrationImageData() : corners_found(false), reprojection_error(0.0) {}
};

/**
 * @brief 外参标定参数结构
 */
struct ExtrinsicCalibrationParams {
    // 标定板参数
    CalibrationBoard::BoardType board_type;
    cv::Size board_size;
    float square_size;

    // PnP求解参数
    int pnp_method;                    // PnP求解方法
    bool use_extrinsic_guess;          // 是否使用外参初值
    int pnp_iterations_count;          // PnP迭代次数
    float pnp_reprojection_error;      // PnP重投影误差阈值
    double pnp_confidence;             // PnP置信度

    // Bundle Adjustment参数
    bool enable_bundle_adjustment;     // 是否启用BA优化
    int ba_max_iterations;             // BA最大迭代次数
    double ba_function_tolerance;      // BA函数收敛阈值
    double ba_parameter_tolerance;     // BA参数收敛阈值
    double ba_gradient_tolerance;      // BA梯度收敛阈值

    // 优化选项
    bool optimize_intrinsics;          // 是否同时优化内参
    bool optimize_distortion;          // 是否同时优化畸变
    bool fix_principal_point;          // 是否固定主点
    bool fix_aspect_ratio;             // 是否固定纵横比
    bool zero_tangent_distortion;      // 是否设置切向畸变为0

    // 质量控制
    double max_reprojection_error;     // 最大允许重投影误差
    int min_required_images;           // 最少需要的图像数量
    double min_baseline_ratio;         // 最小基线比例

    // 角点检测参数
    bool enable_subpixel_refinement;   // 是否启用亚像素精度优化

    ExtrinsicCalibrationParams()
        : board_type(CalibrationBoard::CHESSBOARD)
        , board_size(11, 8)
        , square_size(30.0f)
        , pnp_method(cv::SOLVEPNP_ITERATIVE)
        , use_extrinsic_guess(false)
        , pnp_iterations_count(100)
        , pnp_reprojection_error(8.0f)
        , pnp_confidence(0.99)
        , enable_bundle_adjustment(true)
        , ba_max_iterations(100)
        , ba_function_tolerance(1e-6)
        , ba_parameter_tolerance(1e-8)
        , ba_gradient_tolerance(1e-10)
        , optimize_intrinsics(false)
        , optimize_distortion(false)
        , fix_principal_point(false)
        , fix_aspect_ratio(false)
        , zero_tangent_distortion(false)
        , max_reprojection_error(2.0)
        , min_required_images(3)
        , min_baseline_ratio(0.1)
        , enable_subpixel_refinement(true) {}
};

/**
 * @brief 图像处理参数结构
 */
struct ImageProcessingParams {
    // 图像尺寸
    int src_width;
    int src_height;
    int output_width;
    int output_height;
    
    // 棋盘格参数
    int chess_rows;
    int chess_cols;
    cv::Rect chess_bounds;
    
    // 处理参数
    int bottom_threshold;
    int chess_y_offset;
    int dark_threshold;
    int darken_amount;
    std::vector<double> msrcr_weights;
    std::vector<double> msrcr_sigmas;
    double msrcr_gain;
    double msrcr_offset;

    std::vector<int> morphology_kernel_size;
    int morphology_dilate_iterations;
    int morphology_erode_iterations;

    int border_width;
    int border_bottom_height;
    
    // 世界坐标范围
    double world_x_min, world_x_max;
    double world_y_min, world_y_max;

    // 图像处理范围
    cv::Range img_x_range;
    cv::Range img_y_range;

    // 列点数验证
    std::vector<int> left_column_counts;
    std::vector<int> right_column_counts;
    
    ImageProcessingParams()
        : src_width(1280), src_height(720)
        , output_width(1280), output_height(720)
        , chess_rows(0), chess_cols(0)
        , bottom_threshold(20), chess_y_offset(37)
        , dark_threshold(165), darken_amount(50)
        , world_x_min(0), world_x_max(82)
        , world_y_min(-125), world_y_max(125)
        , img_x_range(30, 1250), img_y_range(400, 720) {}
};

/**
 * @brief Blob 检测参数结构
 */
struct BlobDetectorParams {
    bool filter_by_color;
    int blob_color;
    
    bool filter_by_area;
    float min_area;
    float max_area;
    
    bool filter_by_circularity;
    float min_circularity;
    float max_circularity;
    
    bool filter_by_convexity;
    float min_convexity;
    float max_convexity;
    
    bool filter_by_inertia;
    float min_inertia_ratio;
    float max_inertia_ratio;
    
    BlobDetectorParams()
        : filter_by_color(true), blob_color(0)
        , filter_by_area(true), min_area(200.0f), max_area(100000.0f)
        , filter_by_circularity(true), min_circularity(0.05f), max_circularity(0.99f)
        , filter_by_convexity(false), min_convexity(0.87f), max_convexity(1.0f)
        , filter_by_inertia(false), min_inertia_ratio(0.05f), max_inertia_ratio(0.99f) {}
    
    // 转换为 OpenCV SimpleBlobDetector::Params
    cv::SimpleBlobDetector::Params toOpenCVParams() const;
};

/**
 * @brief 外参标定结果结构
 */
struct ExtrinsicCalibrationResult {
    bool success;                                      // 标定是否成功
    std::vector<CalibrationImageData> image_data;      // 每张图像的标定数据
    std::optional<CameraIntrinsics> optimized_intrinsics;             // 优化后的内参(如果启用内参优化)
    std::optional<DistortionCoefficients> optimized_distortion;       // 优化后的畸变系数

    // 全局外参结果
    std::optional<CameraExtrinsics> final_extrinsics;  // 最终的全局外参
    std::optional<CameraExtrinsics> mean_extrinsics;   // 平均外参
    std::optional<CameraExtrinsics> best_extrinsics;   // 最佳外参（重投影误差最小）

    // 统计信息
    double mean_reprojection_error;                    // 平均重投影误差
    double max_reprojection_error;                     // 最大重投影误差
    std::vector<double> per_image_errors;              // 每张图像的重投影误差
    int num_successful_images;                         // 成功标定的图像数量
    int total_images;                                  // 总图像数量

    // 几何质量评估
    double baseline_coverage;                          // 基线覆盖度
    double rotation_coverage;                          // 旋转覆盖度
    std::vector<double> translation_magnitudes;        // 平移量大小
    std::vector<double> rotation_angles;               // 旋转角度

    ExtrinsicCalibrationResult()
        : success(false)
        , mean_reprojection_error(0.0)
        , max_reprojection_error(0.0)
        , num_successful_images(0)
        , total_images(0)
        , baseline_coverage(0.0)
        , rotation_coverage(0.0) {}

    // 计算统计信息
    void computeStatistics();

    // 保存结果到文件
    bool saveToFile(const std::string& filename) const;

    // 从文件加载结果
    bool loadFromFile(const std::string& filename);
};

/**
 * @brief 标定模式枚举
 */
enum class CalibrationMode {
    INTRINSIC_ONLY,        // 仅内参标定
    EXTRINSIC_ONLY,        // 仅外参标定
    INTRINSIC_EXTRINSIC,   // 内参+外参联合标定
    FORWARD_MAPPING        // 前向映射标定(原有功能)
};

/**
 * @brief 标定统计信息结构
 */
struct CalibrationStats {
    // 基本信息
    bool corners_detected;
    cv::Size image_size;
    std::string status_message;
    std::string error_message;

    // 外参标定相关统计
    int processed_images;
    int successful_detections;
    double detection_rate;

    // 质量指标
    double mean_reprojection_error;
    double std_reprojection_error;
    double max_reprojection_error;
    double min_reprojection_error;

    // 几何覆盖
    double translation_range;
    double rotation_range;

    CalibrationStats()
        : corners_detected(false)
        , processed_images(0)
        , successful_detections(0)
        , detection_rate(0.0)
        , mean_reprojection_error(0.0)
        , std_reprojection_error(0.0)
        , max_reprojection_error(0.0)
        , translation_range(0.0)
        , rotation_range(0.0) {}
};

/**
 * @brief 错误码枚举
 */
enum class ErrorCode {
    SUCCESS = 0,
    FILE_NOT_FOUND = -1,
    INVALID_CONFIG = -2,
    IMAGE_LOAD_FAILED = -3,
    CALIBRATION_FAILED = -4,
    FORWARD_CALIB_FAILED = -5,
    FEATURE_DETECTION_FAILED = -6,
    INSUFFICIENT_POINTS = -7,
    INVALID_PARAMETERS = -8,
    FEATURE_LABELING_FAILED = -9,
    POINT_COUNT_VALIDATION_FAILED = -10,
    TABLE_GENERATION_FAILED = -11,
    GENERAL_ERROR = -12,
    // 外参标定相关错误码
    EXTRINSIC_CALIB_FAILED = -13,
    CORNER_DETECTION_FAILED = -14,
    PNP_SOLVE_FAILED = -15,
    BUNDLE_ADJUSTMENT_FAILED = -16,
    INSUFFICIENT_IMAGES = -17,
    POOR_GEOMETRY = -18,
    HIGH_REPROJECTION_ERROR = -19
};

/**
 * @brief 将错误码转换为字符串
 */
std::string errorCodeToString(ErrorCode code);

} // namespace core
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_CORE_TYPES_H
